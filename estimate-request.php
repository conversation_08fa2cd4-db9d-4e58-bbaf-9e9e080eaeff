<?php
require_once 'config.php';

// Include PHPMailer classes
require_once __DIR__ . '/vendor/PHPMailer/PHPMailer.php';
require_once __DIR__ . '/vendor/PHPMailer/SMTP.php';
require_once __DIR__ . '/vendor/PHPMailer/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get and validate form data
$from_location = trim($_POST['from_location'] ?? '');
$to_location = trim($_POST['to_location'] ?? '');
$pickup_date = trim($_POST['pickup_date'] ?? '');
$material_weight = trim($_POST['material_weight'] ?? '');
$phone_number = trim($_POST['phone_number'] ?? '');

// Validation
$errors = [];

if (empty($from_location) || $from_location === 'Select your location') {
    $errors[] = 'From location is required';
}

if (empty($to_location) || $to_location === 'Select your location') {
    $errors[] = 'To location is required';
}

if (empty($pickup_date)) {
    $errors[] = 'Pickup date is required';
}

if (empty($phone_number)) {
    $errors[] = 'Phone number is required';
}

if (!empty($errors)) {
    echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
    exit;
}

// Generate email content for admin notification
function generateEstimateNotificationEmail($data) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Estimate Request - ' . SITE_NAME . '</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
            .info-row { margin: 15px 0; padding: 10px; background: white; border-radius: 5px; border-left: 4px solid #3b82f6; }
            .label { font-weight: bold; color: #1e40af; }
            .footer { text-align: center; margin-top: 30px; padding: 20px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>New Estimate Request</h1>
                <p>A new shipping estimate request has been submitted</p>
            </div>
            <div class="content">
                <div class="info-row">
                    <span class="label">From Location:</span> ' . htmlspecialchars($data['from_location']) . '
                </div>
                <div class="info-row">
                    <span class="label">To Location:</span> ' . htmlspecialchars($data['to_location']) . '
                </div>
                <div class="info-row">
                    <span class="label">Pickup Date:</span> ' . htmlspecialchars($data['pickup_date']) . '
                </div>
                <div class="info-row">
                    <span class="label">Material Weight:</span> ' . htmlspecialchars($data['material_weight'] ?: 'Not specified') . '
                </div>
                <div class="info-row">
                    <span class="label">Phone Number:</span> ' . htmlspecialchars($data['phone_number']) . '
                </div>
                <div class="info-row">
                    <span class="label">Submitted:</span> ' . date('F j, Y \a\t g:i A') . '
                </div>
            </div>
            <div class="footer">
                <p><strong>' . SITE_NAME . '</strong></p>
                <p>Please contact the customer promptly to provide the requested estimate.</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

try {
    // Prepare data array
    $estimateData = [
        'from_location' => $from_location,
        'to_location' => $to_location,
        'pickup_date' => $pickup_date,
        'material_weight' => $material_weight,
        'phone_number' => $phone_number
    ];

    // Send notification email to company using PHPMailer
    $emailSubject = 'New Estimate Request - ' . SITE_NAME;
    $emailContent = generateEstimateNotificationEmail($estimateData);
    
    $mail = new PHPMailer(true);
    
    // SMTP configuration
    $mail->isSMTP();
    $mail->Host = SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = SMTP_USERNAME;
    $mail->Password = SMTP_PASSWORD;
    $mail->SMTPSecure = SMTP_SECURE;
    $mail->Port = SMTP_PORT;
    
    // Email settings
    $mail->setFrom(SITE_EMAIL, SITE_NAME);
    $mail->addAddress('<EMAIL>'); // Send to company email
    // Note: Phone number cannot be used as reply-to email, using site email instead
    $mail->addReplyTo(SITE_EMAIL, 'Estimate Request');
    
    $mail->isHTML(true);
    $mail->Subject = $emailSubject;
    $mail->Body = $emailContent;
    
    $mail->send();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Thank you for your estimate request! Our team will contact you shortly with a detailed quote.'
    ]);
    
} catch (Exception $e) {
    error_log('Estimate request PHPMailer error: ' . $e->getMessage());
    error_log('PHPMailer ErrorInfo: ' . $mail->ErrorInfo);
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error processing your request. Please try again or contact us directly.'
    ]);
}
?>
