    <!-- Footer -->
    <footer class="bg-gray-50 text-gray-800 pt-16 pb-8">
        <div class="container mx-auto px-4">
            <!-- Newsletter Section -->
            <div class="bg-primary rounded-2xl p-8 mb-12 text-center relative overflow-hidden" style="background-image: linear-gradient(rgba(255, 122, 61, 0.9), rgba(255, 122, 61, 0.9)), url('<?php echo getAssetUrl('assets/img/img5.webp'); ?>'); background-size: cover; background-position: center;">
                <h3 class="text-2xl font-bold text-white mb-4">Subscribe Our Newsletter</h3>
                <p class="text-white/90 mb-6 max-w-2xl mx-auto">
                    Stay updated with the latest logistics trends, shipping updates, and industry insights. Get exclusive offers and expert tips delivered to your inbox.
                </p>
                <form id="newsletterForm" action="newsletter-subscribe.php" method="POST" class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" data-ajax="true">
                    <input type="email" name="email" placeholder="Enter your email address" required
                           class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white transition-all duration-300">
                    <button type="submit" class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 btn-enhanced">
                        Subscribe
                    </button>
                </form>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center mb-6">
                        <div class="logo-container flex flex-col leading-none">
                            <div class="text-primary font-bold text-2xl tracking-wide bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent">
                                PROMTECH
                            </div>
                            <div class="text-gray-800 text-xs font-medium tracking-widest opacity-90">
                                EXPORT & IMPORT
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Leading platform for global trade solutions, connecting markets and empowering businesses worldwide with reliable logistics services.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-facebook-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-twitter-x-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-linkedin-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-instagram-line"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Company -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Company</h3>
                    <ul class="space-y-3">
                        <li><a href="about.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>About Us
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Our Services
                        </a></li>
                        <li><a href="commodities.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Commodities
                        </a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Our Team
                        </a></li>
                        <li><a href="contact.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Contact Us
                        </a></li>
                    </ul>
                </div>
                
                <!-- Services -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Services</h3>
                    <ul class="space-y-3">
                        <li><a href="ocean-freight.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Ocean Freight
                        </a></li>
                        <li><a href="air-freight.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Air Freight
                        </a></li>
                        <li><a href="road-freight.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Road Freight
                        </a></li>
                        <li><a href="warehousing.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Warehousing
                        </a></li>
                        <li><a href="custom-clearance.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Custom Clearance
                        </a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Contact Info</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-map-pin-line"></i>
                            </div>
                            <div>
                                <p class="text-gray-600 leading-relaxed">74 Hrushevsky Street,<br>Kiev, 01008, Ukraine</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-mail-line"></i>
                            </div>
                            <div>
                                <a href="mailto:<EMAIL>" class="text-gray-600 hover:text-primary transition-colors">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-time-line"></i>
                            </div>
                            <div>
                                <p class="text-gray-600">Mon - Fri: 9:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 4:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="border-t border-gray-300 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-600 mb-4 md:mb-0">
                        © <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="privacy-policy.php" class="text-gray-600 hover:text-primary transition-colors">Privacy Policy</a>
                        <a href="terms-of-service.php" class="text-gray-600 hover:text-primary transition-colors">Terms of Service</a>
                        <a href="disclaimer.php" class="text-gray-600 hover:text-primary transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-6 right-6 w-12 h-12 rounded-full text-white shadow-lg z-50 opacity-0 pointer-events-none transition-all duration-300 flex items-center justify-center">
        <i class="ri-arrow-up-line text-xl"></i>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mobile menu script loaded');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            console.log('Mobile menu elements:', {
                btn: mobileMenuBtn,
                menu: mobileMenu,
                overlay: mobileMenuOverlay,
                close: mobileMenuClose
            });

            // Open mobile menu
            if (mobileMenuBtn) {
                console.log('Adding click listener to mobile menu button');
                mobileMenuBtn.addEventListener('click', function() {
                    console.log('Mobile menu button clicked');
                    if (mobileMenu) {
                        console.log('Adding active class to mobile menu');
                        mobileMenu.classList.add('active');
                    }
                    if (mobileMenuOverlay) {
                        console.log('Adding active class to mobile menu overlay');
                        mobileMenuOverlay.classList.add('active');
                    }
                    document.body.style.overflow = 'hidden';
                });
            } else {
                console.log('Mobile menu button not found');
            }

            // Close mobile menu
            function closeMobileMenu() {
                if (mobileMenu) {
                    mobileMenu.classList.remove('active');
                }
                if (mobileMenuOverlay) {
                    mobileMenuOverlay.classList.remove('active');
                }
                document.body.style.overflow = '';
            }

            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', closeMobileMenu);
            }

            if (mobileMenuOverlay) {
                mobileMenuOverlay.addEventListener('click', closeMobileMenu);
            }

            // Desktop dropdown functionality
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('button');
                const menu = dropdown.querySelector('.dropdown-menu');
                let hoverTimeout;

                if (button && menu) {
                    // Show dropdown on hover
                    dropdown.addEventListener('mouseenter', function() {
                        clearTimeout(hoverTimeout);
                        menu.style.display = 'block';
                        setTimeout(() => {
                            menu.style.opacity = '1';
                            menu.style.transform = 'translateY(0)';
                        }, 10);
                    });

                    // Hide dropdown with delay
                    dropdown.addEventListener('mouseleave', function() {
                        hoverTimeout = setTimeout(() => {
                            menu.style.opacity = '0';
                            menu.style.transform = 'translateY(-10px)';
                            setTimeout(() => {
                                menu.style.display = 'none';
                            }, 300);
                        }, 100);
                    });

                    // Keep dropdown open when hovering over menu
                    menu.addEventListener('mouseenter', function() {
                        clearTimeout(hoverTimeout);
                    });

                    menu.addEventListener('mouseleave', function() {
                        hoverTimeout = setTimeout(() => {
                            menu.style.opacity = '0';
                            menu.style.transform = 'translateY(-10px)';
                            setTimeout(() => {
                                menu.style.display = 'none';
                            }, 300);
                        }, 100);
                    });
                }
            });

            // Mobile dropdown toggles
            document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const dropdownKey = this.getAttribute('data-dropdown');
                    const submenu = document.getElementById('submenu-' + dropdownKey);
                    const icon = document.getElementById('icon-' + dropdownKey);

                    if (submenu && icon) {
                        const isActive = submenu.classList.contains('active');

                        // Close all other submenus
                        document.querySelectorAll('.mobile-submenu').forEach(menu => {
                            menu.classList.remove('active');
                        });
                        document.querySelectorAll('.mobile-dropdown-btn i').forEach(i => {
                            i.classList.remove('ri-arrow-up-s-line');
                            i.classList.add('ri-arrow-down-s-line');
                            i.style.transform = 'rotate(0deg)';
                        });

                        // Toggle current submenu
                        if (!isActive) {
                            submenu.classList.add('active');
                            icon.classList.remove('ri-arrow-down-s-line');
                            icon.classList.add('ri-arrow-up-s-line');
                            icon.style.transform = 'rotate(180deg)';
                        }
                    }
                });
            });

            // Close mobile menu when clicking on menu links
            document.querySelectorAll('.mobile-menu-link, .mobile-submenu a').forEach(link => {
                link.addEventListener('click', function() {
                    closeMobileMenu();
                });
            });

            // Improve desktop dropdown behavior
            let dropdownTimeout;
            document.querySelectorAll('.dropdown').forEach(dropdown => {
                const menu = dropdown.querySelector('.dropdown-menu');

                dropdown.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                    menu.style.display = 'block';
                    setTimeout(() => {
                        menu.style.opacity = '1';
                        menu.style.transform = 'translateY(0)';
                    }, 10);
                });

                dropdown.addEventListener('mouseleave', function() {
                    dropdownTimeout = setTimeout(() => {
                        menu.style.opacity = '0';
                        menu.style.transform = 'translateY(-10px)';
                        setTimeout(() => {
                            menu.style.display = 'none';
                        }, 300);
                    }, 100);
                });
            });


        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Add animation classes on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.hover-scale, .bg-white').forEach(el => {
            observer.observe(el);
        });

        // Simple animations and interactions can be added here if needed

        // Testimonial Slider Functionality
        let currentTestimonial = 0;
        const testimonialSlides = document.querySelectorAll('.testimonial-slide');
        const testimonialDots = document.querySelectorAll('.testimonial-dot');

        function showTestimonial(index) {
            // Hide all slides
            testimonialSlides.forEach(slide => {
                slide.classList.remove('active');
            });

            // Remove active class from all dots
            testimonialDots.forEach(dot => {
                dot.classList.remove('active');
                dot.classList.add('bg-gray-300');
                dot.classList.remove('bg-primary');
            });

            // Show current slide
            if (testimonialSlides[index]) {
                testimonialSlides[index].classList.add('active');
            }

            // Activate current dot
            if (testimonialDots[index]) {
                testimonialDots[index].classList.add('active');
                testimonialDots[index].classList.remove('bg-gray-300');
                testimonialDots[index].classList.add('bg-primary');
            }
        }

        // Add click event listeners to dots
        testimonialDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentTestimonial = index;
                showTestimonial(currentTestimonial);
            });
        });

        // Auto-advance testimonials every 5 seconds
        setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonialSlides.length;
            showTestimonial(currentTestimonial);
        }, 5000);

        // Initialize first testimonial
        if (testimonialSlides.length > 0) {
            showTestimonial(0);
        }

        // Smooth Scroll Animation for Navigation Links
        function initializeSmoothScroll() {
            // Get header height for offset calculation
            const header = document.querySelector('header');
            const headerHeight = header ? header.offsetHeight : 0;
            const offset = headerHeight + 20; // Add some padding

            // Enhanced smooth scrolling with offset handling
            function smoothScrollToElement(target, customOffset = offset) {
                if (!target) return;

                const targetPosition = target.getBoundingClientRect().top + window.pageYOffset - customOffset;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Add visual feedback
                target.style.transition = 'background-color 0.3s ease';
                const originalBg = target.style.backgroundColor;
                target.style.backgroundColor = 'rgba(255, 122, 61, 0.1)';
                setTimeout(() => {
                    target.style.backgroundColor = originalBg;
                }, 1000);
            }

            // Add smooth scrolling to all anchor links that point to sections on the same page
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const target = document.querySelector(targetId);

                    if (target) {
                        smoothScrollToElement(target);

                        // Update URL without jumping
                        if (history.pushState) {
                            history.pushState(null, null, targetId);
                        }
                    }
                });
            });

            // Section-to-section navigation with keyboard shortcuts
            const sections = document.querySelectorAll('section, .section');
            let currentSectionIndex = 0;

            function navigateToSection(direction) {
                const newIndex = currentSectionIndex + direction;
                if (newIndex >= 0 && newIndex < sections.length) {
                    currentSectionIndex = newIndex;
                    smoothScrollToElement(sections[currentSectionIndex]);
                }
            }

            // Keyboard navigation (Page Up/Down for section navigation)
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) return; // Skip if modifier keys are pressed

                switch(e.key) {
                    case 'PageDown':
                        e.preventDefault();
                        navigateToSection(1);
                        break;
                    case 'PageUp':
                        e.preventDefault();
                        navigateToSection(-1);
                        break;
                    case 'Home':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                        }
                        break;
                    case 'End':
                        if (e.ctrlKey) {
                            e.preventDefault();
                            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                        }
                        break;
                }
            });

            // Track current section while scrolling
            const sectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const sectionIndex = Array.from(sections).indexOf(entry.target);
                        if (sectionIndex !== -1) {
                            currentSectionIndex = sectionIndex;
                        }
                    }
                });
            }, {
                threshold: 0.5,
                rootMargin: `-${offset}px 0px -50% 0px`
            });

            sections.forEach(section => sectionObserver.observe(section));

            // Add smooth scroll for "back to top" functionality
            const backToTopBtn = document.createElement('button');
            backToTopBtn.innerHTML = '<i class="ri-arrow-up-line"></i>';
            backToTopBtn.className = 'fixed bottom-6 right-6 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-secondary transition-all duration-300 opacity-0 pointer-events-none z-50';
            backToTopBtn.id = 'backToTop';
            backToTopBtn.setAttribute('aria-label', 'Back to top');
            document.body.appendChild(backToTopBtn);

            // Show/hide back to top button with enhanced logic
            let scrollTimeout;
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;

                if (scrolled > 300) {
                    backToTopBtn.classList.remove('opacity-0', 'pointer-events-none');
                    backToTopBtn.classList.add('opacity-100');
                } else {
                    backToTopBtn.classList.add('opacity-0', 'pointer-events-none');
                    backToTopBtn.classList.remove('opacity-100');
                }

                // Add scroll indicator
                clearTimeout(scrollTimeout);
                document.body.classList.add('scrolling');
                scrollTimeout = setTimeout(() => {
                    document.body.classList.remove('scrolling');
                }, 150);
            });

            // Enhanced back to top functionality
            backToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                currentSectionIndex = 0;
            });

            // Add scroll progress indicator
            const progressBar = document.createElement('div');
            progressBar.className = 'fixed top-0 left-0 h-1 bg-primary z-50 transition-all duration-300';
            progressBar.style.width = '0%';
            document.body.appendChild(progressBar);

            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercent = (scrolled / maxScroll) * 100;
                progressBar.style.width = Math.min(scrollPercent, 100) + '%';
            });
        }

        // Image Lazy Loading
        function initializeLazyLoading() {
            const images = document.querySelectorAll('img[data-src]');

            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('fade-in');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                images.forEach(img => imageObserver.observe(img));
            } else {
                // Fallback for older browsers
                images.forEach(img => {
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                });
            }
        }

        // Enhanced Form Handling with Loading States and Error Handling
        function initializeFormEnhancements() {
            const forms = document.querySelectorAll('form');

            forms.forEach(form => {
                const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');

                if (submitBtn) {
                    const originalText = submitBtn.textContent || submitBtn.value;

                    form.addEventListener('submit', function(e) {
                        // Add loading state
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Sending...';
                        submitBtn.classList.add('opacity-75');

                        // Clear previous error messages
                        form.querySelectorAll('.error-message').forEach(error => error.remove());

                        // Basic form validation
                        const requiredFields = form.querySelectorAll('[required]');
                        let hasErrors = false;

                        requiredFields.forEach(field => {
                            if (!field.value.trim()) {
                                hasErrors = true;
                                showFieldError(field, 'This field is required');
                            } else if (field.type === 'email' && !isValidEmail(field.value)) {
                                hasErrors = true;
                                showFieldError(field, 'Please enter a valid email address');
                            }
                        });

                        if (hasErrors) {
                            e.preventDefault();
                            resetSubmitButton(submitBtn, originalText);
                            return false;
                        }

                        // If using AJAX, handle the response
                        if (form.dataset.ajax === 'true') {
                            e.preventDefault();
                            handleAjaxSubmission(form, submitBtn, originalText);
                        }
                    });
                }
            });
        }

        function showFieldError(field, message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-red-500 text-sm mt-1';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
            field.classList.add('border-red-500');

            // Remove error styling when user starts typing
            field.addEventListener('input', function() {
                field.classList.remove('border-red-500');
                const errorMsg = field.parentNode.querySelector('.error-message');
                if (errorMsg) errorMsg.remove();
            });
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function resetSubmitButton(button, originalText) {
            button.disabled = false;
            button.innerHTML = originalText;
            button.classList.remove('opacity-75');
        }

        function handleAjaxSubmission(form, submitBtn, originalText) {
            const formData = new FormData(form);

            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resetSubmitButton(submitBtn, originalText);

                if (data.success) {
                    showNotification('Success! Your message has been sent.', 'success');
                    form.reset();
                } else {
                    showNotification(data.message || 'An error occurred. Please try again.', 'error');
                }
            })
            .catch(error => {
                resetSubmitButton(submitBtn, originalText);
                showNotification('Network error. Please check your connection and try again.', 'error');
            });
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="ri-${type === 'success' ? 'check' : 'error-warning'}-line mr-2"></i>
                    <span>${message}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <i class="ri-close-line"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }

        // FAQ Accordion Functionality
        function initializeFAQ() {
            const faqQuestions = document.querySelectorAll('.faq-question');

            if (faqQuestions.length === 0) {
                return;
            }

            faqQuestions.forEach((question, index) => {
                // Remove any existing event listeners
                question.replaceWith(question.cloneNode(true));
                const newQuestion = document.querySelectorAll('.faq-question')[index];

                newQuestion.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const faqItem = this.closest('.faq-item');
                    const answer = faqItem.querySelector('.faq-answer');
                    const icon = this.querySelector('.faq-icon');

                    if (!answer || !icon) {
                        return;
                    }

                    // Close all other FAQs first
                    document.querySelectorAll('.faq-item').forEach(item => {
                        if (item !== faqItem) {
                            const otherAnswer = item.querySelector('.faq-answer');
                            const otherIcon = item.querySelector('.faq-icon');
                            if (otherAnswer && otherIcon) {
                                otherAnswer.classList.add('hidden');
                                otherIcon.classList.remove('ri-arrow-up-line');
                                otherIcon.classList.add('ri-arrow-down-line');
                            }
                        }
                    });

                    // Toggle current FAQ with smooth animation
                    const isOpen = !answer.classList.contains('hidden');

                    if (isOpen) {
                        answer.style.maxHeight = answer.scrollHeight + 'px';
                        setTimeout(() => {
                            answer.style.maxHeight = '0px';
                            setTimeout(() => {
                                answer.classList.add('hidden');
                                answer.style.maxHeight = '';
                            }, 300);
                        }, 10);
                        icon.classList.remove('ri-arrow-up-line');
                        icon.classList.add('ri-arrow-down-line');
                    } else {
                        answer.classList.remove('hidden');
                        answer.style.maxHeight = '0px';
                        setTimeout(() => {
                            answer.style.maxHeight = answer.scrollHeight + 'px';
                            setTimeout(() => {
                                answer.style.maxHeight = '';
                            }, 300);
                        }, 10);
                        icon.classList.remove('ri-arrow-down-line');
                        icon.classList.add('ri-arrow-up-line');
                    }
                });
            });
        }

        // Page Transition Loading System
        function initializePageTransitions() {
            // Create page loader overlay
            const pageLoader = document.createElement('div');
            pageLoader.id = 'page-loader';
            pageLoader.className = 'fixed inset-0 bg-white z-[9999] flex items-center justify-center transition-opacity duration-500 opacity-0 pointer-events-none';
            pageLoader.innerHTML = `
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
                    <div class="text-gray-600 font-medium">Loading...</div>
                </div>
            `;
            document.body.appendChild(pageLoader);

            // Show page loader
            function showPageLoader() {
                pageLoader.classList.remove('pointer-events-none');
                pageLoader.classList.remove('opacity-0');
                pageLoader.classList.add('opacity-100');
            }

            // Hide page loader
            function hidePageLoader() {
                pageLoader.classList.remove('opacity-100');
                pageLoader.classList.add('opacity-0');
                setTimeout(() => {
                    pageLoader.classList.add('pointer-events-none');
                }, 500);
            }

            // Handle page transitions for internal links
            document.addEventListener('click', function(e) {
                const link = e.target.closest('a');
                if (!link) return;

                const href = link.getAttribute('href');
                if (!href || href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:') ||
                    href.startsWith('http') || link.hasAttribute('target') || link.hasAttribute('download')) {
                    return;
                }

                // Skip if it's a form submission or has special attributes
                if (link.closest('form') || link.hasAttribute('data-no-transition')) {
                    return;
                }

                e.preventDefault();
                showPageLoader();

                // Add a small delay for smooth transition
                setTimeout(() => {
                    window.location.href = href;
                }, 150);
            });

            // Hide loader when page loads
            window.addEventListener('load', hidePageLoader);

            // Also hide on DOMContentLoaded as backup
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', hidePageLoader);
            } else {
                hidePageLoader();
            }

            // Handle browser back/forward buttons
            window.addEventListener('pageshow', function(e) {
                if (e.persisted) {
                    hidePageLoader();
                }
            });
        }

        // Skeleton Loading for Content Sections
        function initializeSkeletonLoading() {
            // Add skeleton loading to images that are lazy loaded
            const lazyImages = document.querySelectorAll('img[data-src]');
            lazyImages.forEach(img => {
                if (!img.classList.contains('skeleton-added')) {
                    img.classList.add('skeleton-added');

                    // Create skeleton placeholder
                    const skeleton = document.createElement('div');
                    skeleton.className = 'skeleton-placeholder absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse';
                    skeleton.style.backgroundSize = '200% 100%';
                    skeleton.style.animation = 'skeleton-loading 1.5s ease-in-out infinite';

                    // Position parent relatively if not already
                    const parent = img.parentElement;
                    if (getComputedStyle(parent).position === 'static') {
                        parent.style.position = 'relative';
                    }

                    parent.appendChild(skeleton);

                    // Remove skeleton when image loads
                    img.addEventListener('load', () => {
                        skeleton.remove();
                    });
                }
            });
        }

        // Enhanced Form Loading States (extends existing functionality)
        function enhanceFormLoadingStates() {
            const forms = document.querySelectorAll('form[data-ajax="true"]');
            forms.forEach(form => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (!submitBtn) return;

                // Create enhanced loading state
                const originalText = submitBtn.innerHTML;
                const loadingHTML = `
                    <div class="flex items-center justify-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        <span>Processing...</span>
                    </div>
                `;

                form.addEventListener('submit', function() {
                    submitBtn.innerHTML = loadingHTML;
                    submitBtn.disabled = true;
                    submitBtn.classList.add('opacity-75', 'cursor-not-allowed');
                });

                // Reset button state on form reset or error
                form.addEventListener('reset', function() {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                });
            });
        }

        // Search Functionality
        function initializeSearch() {
            const searchInput = document.getElementById('header-search');
            const searchInputPage = document.getElementById('search-input');

            if (searchInput) {
                // Add search suggestions for header search
                let searchTimeout;

                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const query = this.value.trim();

                    if (query.length >= 2) {
                        searchTimeout = setTimeout(() => {
                            fetchSearchSuggestions(query, searchInput);
                        }, 300);
                    } else {
                        hideSearchSuggestions();
                    }
                });

                // Hide suggestions when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.search-container')) {
                        hideSearchSuggestions();
                    }
                });
            }

            if (searchInputPage) {
                // Enhanced search for search page
                let searchTimeout;

                searchInputPage.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    const query = this.value.trim();

                    if (query.length >= 2) {
                        searchTimeout = setTimeout(() => {
                            fetchSearchSuggestions(query, searchInputPage);
                        }, 300);
                    } else {
                        hideSearchSuggestions();
                    }
                });
            }
        }

        function fetchSearchSuggestions(query, inputElement) {
            // Create suggestions dropdown if it doesn't exist
            let suggestionsContainer = inputElement.parentElement.querySelector('.search-suggestions');
            if (!suggestionsContainer) {
                suggestionsContainer = document.createElement('div');
                suggestionsContainer.className = 'search-suggestions absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-50 max-h-60 overflow-y-auto';
                inputElement.parentElement.appendChild(suggestionsContainer);
            }

            // Mock suggestions based on searchable content
            const suggestions = [
                'Ocean Freight Services',
                'Air Freight Services',
                'Road Freight Services',
                'Warehousing Services',
                'Custom Clearance Services',
                'Grain Market',
                'Wood Market',
                'All Commodities'
            ].filter(item => item.toLowerCase().includes(query.toLowerCase()));

            if (suggestions.length > 0) {
                suggestionsContainer.innerHTML = suggestions.slice(0, 5).map(suggestion =>
                    `<div class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0" onclick="selectSuggestion('${suggestion}', '${inputElement.id}')">${suggestion}</div>`
                ).join('');
                suggestionsContainer.style.display = 'block';
            } else {
                suggestionsContainer.style.display = 'none';
            }
        }

        function selectSuggestion(suggestion, inputId) {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = suggestion;
                hideSearchSuggestions();

                // Submit the form
                const form = input.closest('form');
                if (form) {
                    form.submit();
                }
            }
        }

        function hideSearchSuggestions() {
            const suggestions = document.querySelectorAll('.search-suggestions');
            suggestions.forEach(container => {
                container.style.display = 'none';
            });
        }

        // Keyboard shortcuts for search
        function initializeSearchShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + K to focus search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.getElementById('header-search') || document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }

                // Escape to clear search and hide suggestions
                if (e.key === 'Escape') {
                    const activeSearch = document.activeElement;
                    if (activeSearch && (activeSearch.id === 'header-search' || activeSearch.id === 'search-input')) {
                        activeSearch.blur();
                        hideSearchSuggestions();
                    }
                }
            });
        }

        // Initialize all functionality with a small delay to ensure DOM is ready
        setTimeout(() => {
            initializeSmoothScroll();
            initializeLazyLoading();
            initializeFormEnhancements();
            initializeFAQ();
            initializePageTransitions();
            initializeSkeletonLoading();
            enhanceFormLoadingStates();
            initializeSearch();
            initializeSearchShortcuts();
        }, 100);

        }); // Close main DOMContentLoaded event listener

    </script>
</body>
</html>
