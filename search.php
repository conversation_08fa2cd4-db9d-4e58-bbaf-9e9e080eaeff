<?php
require_once 'config.php';

$query = isset($_GET['q']) ? trim($_GET['q']) : '';
$category = isset($_GET['category']) ? trim($_GET['category']) : '';
$results = [];
$suggestions = [];

if (!empty($query)) {
    $results = performSearch($query, $category);
    if (empty($results)) {
        $suggestions = getSearchSuggestions($query);
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo !empty($query) ? 'Search Results for "' . htmlspecialchars($query) . '"' : 'Search'; ?> - PROMTECH Export & Import</title>
    <meta name="description" content="Search results for PROMTECH Export & Import services, commodities, and information.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        'sm': '640px',
                        'md': '768px',
                        'lg': '1024px',
                        'xl': '1280px',
                        '2xl': '1400px'
                    }
                },
                extend: {
                    colors: {
                        primary: '#FF7A3D',
                        secondary: '#1E3A8A',
                        accent: '#F59E0B'
                    },
                    maxWidth: {
                        'container': '1400px'
                    }
                }
            }
        }
    </script>
    
    <!-- RemixIcon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <?php include 'includes/header.php'; ?>



    <!-- Search Results Section -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <!-- Search Header -->
            <div class="max-w-4xl mx-auto mb-8">
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold text-secondary mb-4">
                        <?php if (!empty($query)): ?>
                            Search Results for "<?php echo htmlspecialchars($query); ?>"
                        <?php else: ?>
                            Search Our Services & Commodities
                        <?php endif; ?>
                    </h1>
                    
                    <!-- Enhanced Search Form -->
                    <form method="GET" action="search.php" class="max-w-2xl mx-auto">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1 relative">
                                <input 
                                    type="text" 
                                    name="q" 
                                    value="<?php echo htmlspecialchars($query); ?>"
                                    placeholder="Search services, commodities, or information..."
                                    class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                    id="search-input"
                                >
                                <i class="ri-search-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                
                                <!-- Search Suggestions Dropdown -->
                                <div id="search-suggestions" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 hidden z-50">
                                    <!-- Suggestions will be populated by JavaScript -->
                                </div>
                            </div>
                            
                            <div class="md:w-48">
                                <select name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">All Categories</option>
                                    <option value="Services" <?php echo $category === 'Services' ? 'selected' : ''; ?>>Services</option>
                                    <option value="Markets" <?php echo $category === 'Markets' ? 'selected' : ''; ?>>Markets</option>
                                    <option value="Commodities" <?php echo $category === 'Commodities' ? 'selected' : ''; ?>>Commodities</option>
                                    <option value="Company" <?php echo $category === 'Company' ? 'selected' : ''; ?>>Company</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="px-8 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition-colors font-medium">
                                <i class="ri-search-line mr-2"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Search Results -->
            <?php if (!empty($query)): ?>
                <div class="max-w-4xl mx-auto">
                    <?php if (!empty($results)): ?>
                        <div class="mb-6">
                            <p class="text-gray-600">
                                Found <?php echo count($results); ?> result<?php echo count($results) !== 1 ? 's' : ''; ?>
                                <?php if (!empty($category)): ?>
                                    in <strong><?php echo htmlspecialchars($category); ?></strong>
                                <?php endif; ?>
                            </p>
                        </div>

                        <div class="space-y-6">
                            <?php foreach ($results as $result): ?>
                                <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                                    <div class="flex items-start space-x-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                                                <i class="<?php echo $result['icon']; ?> text-primary text-xl"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <h3 class="text-xl font-semibold text-secondary">
                                                    <a href="<?php echo $result['url']; ?>" class="hover:text-primary transition-colors">
                                                        <?php echo $result['highlighted_title']; ?>
                                                    </a>
                                                </h3>
                                                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                                                    <?php echo $result['category']; ?>
                                                </span>
                                            </div>
                                            <p class="text-gray-600 mb-3">
                                                <?php echo $result['highlighted_description']; ?>
                                            </p>
                                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                                <span class="flex items-center">
                                                    <i class="ri-link mr-1"></i>
                                                    <?php echo $result['url']; ?>
                                                </span>
                                                <span class="flex items-center">
                                                    <i class="ri-star-line mr-1"></i>
                                                    Relevance: <?php echo $result['score']; ?>%
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                    <?php else: ?>
                        <!-- No Results Found -->
                        <div class="text-center py-12">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="ri-search-line text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-2xl font-semibold text-gray-700 mb-4">No results found</h3>
                            <p class="text-gray-600 mb-6">
                                We couldn't find any results for "<strong><?php echo htmlspecialchars($query); ?></strong>"
                                <?php if (!empty($category)): ?>
                                    in <strong><?php echo htmlspecialchars($category); ?></strong>
                                <?php endif; ?>
                            </p>

                            <?php if (!empty($suggestions)): ?>
                                <div class="mb-6">
                                    <p class="text-gray-600 mb-3">Did you mean:</p>
                                    <div class="flex flex-wrap justify-center gap-2">
                                        <?php foreach ($suggestions as $suggestion): ?>
                                            <a href="search.php?q=<?php echo urlencode($suggestion); ?><?php echo !empty($category) ? '&category=' . urlencode($category) : ''; ?>" 
                                               class="px-3 py-1 bg-primary/10 text-primary rounded-full hover:bg-primary hover:text-white transition-colors">
                                                <?php echo htmlspecialchars($suggestion); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="space-y-2 text-sm text-gray-500">
                                <p>Try:</p>
                                <ul class="space-y-1">
                                    <li>• Checking your spelling</li>
                                    <li>• Using different keywords</li>
                                    <li>• Searching in a different category</li>
                                    <li>• Using more general terms</li>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Popular Searches -->
            <div class="max-w-4xl mx-auto mt-12">
                <h3 class="text-xl font-semibold text-secondary mb-6">Popular Searches</h3>
                <div class="flex flex-wrap gap-3">
                    <?php
                    $popularSearches = ['Ocean Freight', 'Air Cargo', 'Grain Export', 'Wood Import', 'Customs Clearance', 'Warehousing', 'Road Transport'];
                    foreach ($popularSearches as $search): ?>
                        <a href="search.php?q=<?php echo urlencode($search); ?>" 
                           class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full hover:bg-primary hover:text-white transition-colors">
                            <?php echo $search; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
