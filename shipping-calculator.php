<?php
require_once 'config.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] === 'calculate') {
        $origin = $_POST['origin'] ?? '';
        $destination = $_POST['destination'] ?? '';
        $weight = floatval($_POST['weight'] ?? 0);
        $service_type = $_POST['service_type'] ?? '';
        $cargo_type = $_POST['cargo_type'] ?? 'general';
        
        $dimensions = null;
        if (!empty($_POST['length']) && !empty($_POST['width']) && !empty($_POST['height'])) {
            $dimensions = [
                'length' => floatval($_POST['length']),
                'width' => floatval($_POST['width']),
                'height' => floatval($_POST['height'])
            ];
        }
        
        $result = calculateShippingCost($service_type, $origin, $destination, $weight, $dimensions, $cargo_type);
        echo json_encode($result);
        exit;
    }
    
    if ($_POST['action'] === 'compare') {
        $origin = $_POST['origin'] ?? '';
        $destination = $_POST['destination'] ?? '';
        $weight = floatval($_POST['weight'] ?? 0);
        $cargo_type = $_POST['cargo_type'] ?? 'general';
        
        $dimensions = null;
        if (!empty($_POST['length']) && !empty($_POST['width']) && !empty($_POST['height'])) {
            $dimensions = [
                'length' => floatval($_POST['length']),
                'width' => floatval($_POST['width']),
                'height' => floatval($_POST['height'])
            ];
        }
        
        $result = compareShippingServices($origin, $destination, $weight, $dimensions, $cargo_type);
        echo json_encode($result);
        exit;
    }
}

$pageTitle = 'Shipping Calculator - Calculate Your Shipping Costs';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="Calculate shipping costs for ocean freight, air freight, and road freight. Get instant quotes for FCL, LCL, express air, and standard shipping services.">
    <meta name="keywords" content="shipping calculator, freight calculator, ocean freight cost, air freight cost, shipping quote, logistics calculator">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo getAssetUrl('assets/img/favicon.ico'); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        'sm': '640px',
                        'md': '768px',
                        'lg': '1024px',
                        'xl': '1280px',
                        '2xl': '1400px'
                    }
                },
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        secondary: '#2C3E50',
                        accent: '#F39C12'
                    },
                    maxWidth: {
                        'container': '1400px'
                    }
                }
            }
        }
    </script>
    
    <!-- RemixIcon -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <style>
        .btn-enhanced {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .rounded-button {
            border-radius: 50px;
        }
        
        .calculator-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .calculator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .form-input {
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.2);
        }
        
        .result-card {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
            animation: slideInUp 0.5s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #FF6B35;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header -->
    <?php include 'includes/header.php'; ?>
    

    
    <!-- Hero Section -->
    <section class="py-16 bg-gradient-to-r from-primary to-secondary">
        <div class="container mx-auto px-4">
            <div class="text-center text-white">
                <h1 class="text-5xl font-bold mb-6">Shipping Calculator</h1>
                <p class="text-xl mb-8 max-w-3xl mx-auto">
                    Calculate shipping costs instantly for ocean freight, air freight, and road freight services. 
                    Get accurate quotes and compare different shipping options for your cargo.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <div class="bg-white/20 px-6 py-3 rounded-full">
                        <i class="ri-calculator-line mr-2"></i>Instant Calculations
                    </div>
                    <div class="bg-white/20 px-6 py-3 rounded-full">
                        <i class="ri-compare-line mr-2"></i>Service Comparison
                    </div>
                    <div class="bg-white/20 px-6 py-3 rounded-full">
                        <i class="ri-global-line mr-2"></i>Global Routes
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Calculator Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-12">
                    <!-- Calculator Form -->
                    <div class="calculator-card p-8 rounded-2xl shadow-lg">
                        <h2 class="text-3xl font-bold text-secondary mb-6">Calculate Shipping Cost</h2>
                        <form id="calculatorForm" class="space-y-6">
                            <!-- Route Information -->
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Origin *</label>
                                    <select name="origin" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option value="">Select origin</option>
                                        <option value="Ukraine">Ukraine</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Destination *</label>
                                    <select name="destination" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option value="">Select destination</option>
                                        <option value="Europe">Europe</option>
                                        <option value="Asia">Asia</option>
                                        <option value="North America">North America</option>
                                        <option value="South America">South America</option>
                                        <option value="Africa">Africa</option>
                                        <option value="Oceania">Oceania</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Service Type -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Service Type *</label>
                                <select name="service_type" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select service type</option>
                                    <option value="ocean_fcl">Ocean Freight - FCL (Full Container)</option>
                                    <option value="ocean_lcl">Ocean Freight - LCL (Less than Container)</option>
                                    <option value="air_express">Air Freight - Express</option>
                                    <option value="air_standard">Air Freight - Standard</option>
                                    <option value="road_freight">Road Freight</option>
                                </select>
                            </div>
                            
                            <!-- Cargo Details -->
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Weight (kg) *</label>
                                    <input type="number" name="weight" required min="1" step="0.1" 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                           placeholder="Enter weight in kg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Cargo Type</label>
                                    <select name="cargo_type" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option value="general">General Cargo</option>
                                        <option value="hazardous">Hazardous Materials</option>
                                        <option value="perishable">Perishable Goods</option>
                                        <option value="fragile">Fragile Items</option>
                                        <option value="oversized">Oversized Cargo</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Dimensions (Optional) -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Dimensions (cm) - Optional</label>
                                <div class="grid grid-cols-3 gap-4">
                                    <input type="number" name="length" min="1" step="0.1" 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                           placeholder="Length">
                                    <input type="number" name="width" min="1" step="0.1" 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                           placeholder="Width">
                                    <input type="number" name="height" min="1" step="0.1" 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                           placeholder="Height">
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-4">
                                <button type="submit" 
                                        class="flex-1 bg-primary text-white py-4 px-8 rounded-button font-semibold text-lg btn-enhanced hover:bg-primary/90 transition-colors flex items-center justify-center">
                                    <i class="ri-calculator-line mr-2"></i>
                                    Calculate Cost
                                </button>
                                <button type="button" id="compareBtn"
                                        class="flex-1 bg-secondary text-white py-4 px-8 rounded-button font-semibold text-lg btn-enhanced hover:bg-secondary/90 transition-colors flex items-center justify-center">
                                    <i class="ri-compare-line mr-2"></i>
                                    Compare Services
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Results Area -->
                    <div id="resultsArea" class="space-y-6">
                        <!-- Welcome Message -->
                        <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-8 rounded-2xl shadow-lg text-center">
                            <div class="bg-primary/10 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                                <i class="ri-calculator-line text-primary text-3xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-secondary mb-4">Ready to Calculate?</h3>
                            <p class="text-gray-600 mb-6">
                                Fill out the form on the left to get instant shipping cost calculations and compare different service options.
                            </p>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="bg-white p-4 rounded-lg">
                                    <i class="ri-ship-line text-primary text-xl mb-2"></i>
                                    <div class="font-semibold">Ocean Freight</div>
                                    <div class="text-gray-600">FCL & LCL Options</div>
                                </div>
                                <div class="bg-white p-4 rounded-lg">
                                    <i class="ri-plane-line text-primary text-xl mb-2"></i>
                                    <div class="font-semibold">Air Freight</div>
                                    <div class="text-gray-600">Express & Standard</div>
                                </div>
                                <div class="bg-white p-4 rounded-lg">
                                    <i class="ri-truck-line text-primary text-xl mb-2"></i>
                                    <div class="font-semibold">Road Freight</div>
                                    <div class="text-gray-600">Overland Transport</div>
                                </div>
                                <div class="bg-white p-4 rounded-lg">
                                    <i class="ri-global-line text-primary text-xl mb-2"></i>
                                    <div class="font-semibold">Global Routes</div>
                                    <div class="text-gray-600">Worldwide Coverage</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-secondary mb-4">How Our Calculator Works</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Get accurate shipping cost estimates in just a few simple steps
                </p>
            </div>

            <div class="grid md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-xl font-bold text-secondary mb-2">Enter Details</h3>
                    <p class="text-gray-600">Provide origin, destination, weight, and cargo type</p>
                </div>

                <div class="text-center">
                    <div class="bg-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-xl font-bold text-secondary mb-2">Select Service</h3>
                    <p class="text-gray-600">Choose from ocean, air, or road freight options</p>
                </div>

                <div class="text-center">
                    <div class="bg-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-xl font-bold text-secondary mb-2">Get Quote</h3>
                    <p class="text-gray-600">Receive instant cost calculation with breakdown</p>
                </div>

                <div class="text-center">
                    <div class="bg-primary w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">4</span>
                    </div>
                    <h3 class="text-xl font-bold text-secondary mb-2">Compare & Book</h3>
                    <p class="text-gray-600">Compare services and request official quote</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-primary to-secondary">
        <div class="container mx-auto px-4">
            <div class="text-center text-white">
                <h2 class="text-4xl font-bold mb-4">Need a Detailed Quote?</h2>
                <p class="text-xl mb-8 max-w-2xl mx-auto">
                    Our calculator provides estimates. For official quotes and booking, contact our logistics experts.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="quote.php" class="bg-white text-primary px-8 py-4 rounded-button font-semibold text-lg btn-enhanced hover:bg-gray-100 transition-colors inline-flex items-center justify-center">
                        <i class="ri-file-text-line mr-2"></i>Request Official Quote
                    </a>
                    <a href="contact.php" class="bg-white/20 text-white px-8 py-4 rounded-button font-semibold text-lg btn-enhanced hover:bg-white/30 transition-colors inline-flex items-center justify-center">
                        <i class="ri-phone-line mr-2"></i>Contact Expert
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Include Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Calculator JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calculatorForm = document.getElementById('calculatorForm');
            const compareBtn = document.getElementById('compareBtn');
            const resultsArea = document.getElementById('resultsArea');

            // Calculate single service cost
            calculatorForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('action', 'calculate');

                showLoading();

                fetch('shipping-calculator.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showError(data.error);
                    } else {
                        showSingleResult(data);
                    }
                })
                .catch(error => {
                    showError('An error occurred while calculating. Please try again.');
                });
            });

            // Compare all services
            compareBtn.addEventListener('click', function() {
                const formData = new FormData(calculatorForm);

                // Validate required fields
                if (!formData.get('origin') || !formData.get('destination') || !formData.get('weight')) {
                    showError('Please fill in origin, destination, and weight to compare services.');
                    return;
                }

                formData.append('action', 'compare');

                showLoading();

                fetch('shipping-calculator.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    showComparisonResults(data);
                })
                .catch(error => {
                    showError('An error occurred while comparing services. Please try again.');
                });
            });

            function showLoading() {
                resultsArea.innerHTML = `
                    <div class="bg-white p-8 rounded-2xl shadow-lg text-center">
                        <div class="loading-spinner mx-auto mb-4"></div>
                        <h3 class="text-xl font-semibold text-secondary mb-2">Calculating...</h3>
                        <p class="text-gray-600">Please wait while we calculate your shipping costs.</p>
                    </div>
                `;
            }

            function showError(message) {
                resultsArea.innerHTML = `
                    <div class="bg-red-50 border border-red-200 p-8 rounded-2xl shadow-lg text-center">
                        <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="ri-error-warning-line text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-red-800 mb-2">Error</h3>
                        <p class="text-red-600">${message}</p>
                    </div>
                `;
            }

            function showSingleResult(data) {
                const service = data.service;
                const additionalFeesHtml = Object.entries(data.additional_fees)
                    .map(([key, value]) => `
                        <div class="flex justify-between">
                            <span class="capitalize">${key.replace('_', ' ')}</span>
                            <span>€${value.toFixed(2)}</span>
                        </div>
                    `).join('');

                resultsArea.innerHTML = `
                    <div class="result-card p-8 rounded-2xl shadow-lg text-white">
                        <div class="flex items-center mb-6">
                            <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mr-4">
                                <i class="ri-calculator-line text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold">${service.name}</h3>
                                <p class="text-white/80">${service.description}</p>
                            </div>
                        </div>

                        <div class="bg-white/10 p-6 rounded-xl mb-6">
                            <div class="text-center mb-4">
                                <div class="text-4xl font-bold mb-2">€${data.total_cost}</div>
                                <div class="text-white/80">Total Estimated Cost</div>
                            </div>

                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="text-center">
                                    <div class="font-semibold">Transit Time</div>
                                    <div class="text-white/80">${data.transit_time}</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-semibold">Base Cost</div>
                                    <div class="text-white/80">€${data.base_cost}</div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white/10 p-6 rounded-xl mb-6">
                            <h4 class="font-semibold mb-4">Cost Breakdown</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span>Base Rate</span>
                                    <span>€${data.base_cost}</span>
                                </div>
                                ${additionalFeesHtml}
                                <hr class="border-white/20 my-2">
                                <div class="flex justify-between font-semibold">
                                    <span>Total</span>
                                    <span>€${data.total_cost}</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white/10 p-6 rounded-xl">
                            <h4 class="font-semibold mb-4">Service Features</h4>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                ${service.features.map(feature => `
                                    <div class="flex items-center">
                                        <i class="ri-check-line mr-2"></i>
                                        ${feature}
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="mt-6 text-center">
                            <a href="quote.php" class="bg-white text-primary px-6 py-3 rounded-button font-semibold hover:bg-gray-100 transition-colors inline-flex items-center">
                                <i class="ri-file-text-line mr-2"></i>
                                Request Official Quote
                            </a>
                        </div>
                    </div>
                `;
            }

            function showComparisonResults(data) {
                if (Object.keys(data).length === 0) {
                    showError('No services available for the selected route.');
                    return;
                }

                const services = Object.entries(data);
                const cheapest = services[0]; // Already sorted by price

                const comparisonHtml = services.map(([serviceType, result], index) => {
                    const service = result.service;
                    const isRecommended = index === 0;
                    const isFastest = services.every(([, other]) =>
                        parseInt(result.transit_time) <= parseInt(other.transit_time)
                    );

                    return `
                        <div class="bg-white p-6 rounded-2xl shadow-lg border-2 ${isRecommended ? 'border-primary' : 'border-gray-200'} relative">
                            ${isRecommended ? `
                                <div class="absolute -top-3 left-6 bg-primary text-white px-4 py-1 rounded-full text-sm font-semibold">
                                    Best Value
                                </div>
                            ` : ''}
                            ${isFastest ? `
                                <div class="absolute -top-3 right-6 bg-secondary text-white px-4 py-1 rounded-full text-sm font-semibold">
                                    Fastest
                                </div>
                            ` : ''}

                            <div class="text-center mb-4">
                                <h3 class="text-xl font-bold text-secondary mb-2">${service.name}</h3>
                                <p class="text-gray-600 text-sm">${service.description}</p>
                            </div>

                            <div class="text-center mb-6">
                                <div class="text-3xl font-bold text-primary mb-1">€${result.total_cost}</div>
                                <div class="text-gray-600 text-sm">Total Cost</div>
                            </div>

                            <div class="grid grid-cols-2 gap-4 mb-6 text-sm">
                                <div class="text-center">
                                    <div class="font-semibold text-secondary">Transit Time</div>
                                    <div class="text-gray-600">${result.transit_time}</div>
                                </div>
                                <div class="text-center">
                                    <div class="font-semibold text-secondary">Base Rate</div>
                                    <div class="text-gray-600">€${result.base_cost}</div>
                                </div>
                            </div>

                            <div class="mb-6">
                                <h4 class="font-semibold text-secondary mb-3">Features</h4>
                                <div class="space-y-2 text-sm">
                                    ${service.features.slice(0, 3).map(feature => `
                                        <div class="flex items-center text-gray-600">
                                            <i class="ri-check-line text-primary mr-2"></i>
                                            ${feature}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>

                            <button onclick="selectService('${serviceType}')"
                                    class="w-full bg-primary text-white py-3 rounded-button font-semibold hover:bg-primary/90 transition-colors">
                                Select This Service
                            </button>
                        </div>
                    `;
                }).join('');

                resultsArea.innerHTML = `
                    <div class="space-y-6">
                        <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-2xl shadow-lg">
                            <h3 class="text-2xl font-bold text-secondary mb-4 text-center">Service Comparison</h3>
                            <p class="text-gray-600 text-center mb-4">
                                Compare different shipping options for your cargo. Prices are estimates and may vary.
                            </p>
                            <div class="grid grid-cols-3 gap-4 text-center text-sm">
                                <div>
                                    <div class="font-semibold text-secondary">${services.length}</div>
                                    <div class="text-gray-600">Services Available</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-secondary">€${cheapest[1].total_cost}</div>
                                    <div class="text-gray-600">Best Price</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-secondary">${Math.min(...services.map(([, result]) => parseInt(result.transit_time)))}-${Math.max(...services.map(([, result]) => parseInt(result.transit_time.split('-')[1] || result.transit_time)))} days</div>
                                    <div class="text-gray-600">Transit Range</div>
                                </div>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                            ${comparisonHtml}
                        </div>

                        <div class="bg-gradient-to-r from-primary to-secondary p-6 rounded-2xl shadow-lg text-white text-center">
                            <h4 class="text-xl font-bold mb-2">Need Help Choosing?</h4>
                            <p class="mb-4">Our logistics experts can help you select the best shipping option for your needs.</p>
                            <a href="contact.php" class="bg-white text-primary px-6 py-3 rounded-button font-semibold hover:bg-gray-100 transition-colors inline-flex items-center">
                                <i class="ri-phone-line mr-2"></i>
                                Contact Expert
                            </a>
                        </div>
                    </div>
                `;
            }

            function selectService(serviceType) {
                // Set the service type in the form
                const serviceSelect = document.querySelector('select[name="service_type"]');
                serviceSelect.value = serviceType;

                // Trigger calculation for the selected service
                calculatorForm.dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
