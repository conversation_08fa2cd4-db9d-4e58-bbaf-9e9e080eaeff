{"timestamp":"2025-07-06 04:53:13","type":"contact","ip":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","data":{"firstName":"<PERSON>","lastName":"<PERSON>","email":"<EMAIL>","phone":"+1234567890","company":"","subject":"General Inquiry","message":"This is a test message to verify that the contact form email functionality is working properly. Please confirm receipt of this test email.","privacy":"on"}}
{"timestamp":"2025-07-06 05:00:09","type":"quote","ip":"::1","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHT<PERSON>, like Gecko) Chrome\/********* <PERSON><PERSON>\/537.36","data":{"firstName":"<PERSON>","lastName":"Doe","email":"<EMAIL>","phone":"+9876543210","company":"","originCountry":"Ukraine","destination":"Europe","serviceType":"Ocean Freight","weight":"","length":"","width":"","height":"","commodityType":"","pickupDate":"","requirements":""}}
